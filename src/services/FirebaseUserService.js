// FirebaseUserService.js (stub)
// Temporary stub after Firebase removal to satisfy legacy imports.

/* eslint-disable no-console */

export async function addRoutineItem(userId, itemData) {
  console.warn('[Stub] addRoutineItem called but Firebase is removed.');
  return { success: false };
}

export async function clearPendingAddItem(userId, threadId) {
  console.warn('[Stub] clearPendingAddItem called but Firebase is removed.');
  return { success: false };
} 